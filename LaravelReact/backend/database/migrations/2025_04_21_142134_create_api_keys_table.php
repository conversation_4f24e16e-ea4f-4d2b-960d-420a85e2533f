<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Purpose/client name
            $table->string('key')->unique(); // The actual API key
            $table->text('permissions')->nullable(); // Optional scopes/permissions
            $table->boolean('active')->default(true);
            $table->timestamp('expires_at')->nullable(); // Optional expiration
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
