.nav-profile {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: indigo;
  cursor: pointer;
}

.page-item {
  width: 46px;
  height: 46px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
  background: #fff;
  color: #101115;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 7px;
}

.github {
  height: 36px;
  width: 36px;
  line-height: 36px;
  transition: all 0.3s ease;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: #0f9d58;
  color: #fff;
}

/* Breadcrumb styles */
.breadcrumb-item {
  color: #fff !important;
}

.breadcrumb-item + .breadcrumb-item::before {
  color: #fff !important;
  content: "/" !important;
}

.breadcrumb-item.active {
  color: #fff !important;
}

/* Page header styles */
.pageheader-section {
  transition: background-image 0.3s ease-in-out;
}

.pageheader-content h2 {
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Enhance navigation visibility */
.nav-link {
  color: #fff !important;
  transition: all 0.3s ease;
}

.nav-link:hover {
  opacity: 0.8;
}

/* Mobile/Tablet Navigation Styles with Bootstrap */
@media screen and (max-width: 992px) {
  .navbar-collapse.show,
  .navbar-collapse.collapsing,
  .menu .lab-ul.active {
    background-color: #fff !important;
    padding: 1rem 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .menu .lab-ul.active li a,
  .navbar-nav .nav-link,
  .navbar-nav .nav-item a {
    color: #000 !important;
    padding: 0.75rem 1.25rem !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .menu .lab-ul.active li:last-child a,
  .navbar-nav .nav-item:last-child a {
    border-bottom: none;
  }

  .navbar-toggler-icon,
  .header-bar span {
    background-color: #000 !important;
  }

  .header-bottom {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
}

/* Custom styles for quantity control buttons */
.qty-btn {
  width: 40px !important;
  height: 40px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.25rem !important;
}

.qty-btn i {
  font-size: inherit;
  line-height: 1;
}
